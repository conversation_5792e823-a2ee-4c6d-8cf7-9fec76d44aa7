# App
NODE_ENV=development
PORT=3000
API_PREFIX=v1

# Database
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_DATABASE=redai_db
DB_SSL=false

# Cloudflare R2
CF_R2_ACCESS_KEY=mock_access_key
CF_R2_SECRET_KEY=mock_secret_key
CF_R2_ENDPOINT=https://mock-endpoint.com
CF_BUCKET_NAME=mock-bucket

# OpenAI
OPENAI_API_KEY=********************************************************************************************************************************************************************
OPENAI_ORGANIZATION_ID=mock_org_id

# CDN
CDN_URL=https://mock-cdn.com
CDN_SECRET_KEY=mock_cdn_key

# JWT Authentication
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRATION_TIME=1d
JWT_REFRESH_SECRET=your_jwt_refresh_secret_key_here
JWT_REFRESH_EXPIRATION_TIME=7d

# Redis
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# Agent API Key
AGENT_API_KEY=1a103477-1234-4123-8a3b-23330887fa2a
