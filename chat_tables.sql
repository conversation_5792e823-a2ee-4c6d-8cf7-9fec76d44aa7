-- SQL để tạo các bảng cho module Chat
-- Thự<PERSON> hiện theo thứ tự: facebook_page_configs -> chat_conversations -> chat_messages

-- 1. Bảng facebook_page_configs
CREATE TABLE facebook_page_configs (
    page_id VARCHAR NOT NULL PRIMARY KEY,
    page_name VARCHAR NOT NULL,
    access_token VARCHAR NOT NULL,
    tenant_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    webhook_enabled_at BIGINT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at BIGINT DEFAULT ((EXTRACT(EPOCH FROM NOW()) * 1000)::BIGINT),
    updated_at BIGINT DEFAULT ((EXTRACT(EPOCH FROM NOW()) * 1000)::BIGINT),
    deleted_at BIGINT NULL
);

-- 2. Bảng chat_conversations
CREATE TABLE chat_conversations (
    id SERIAL PRIMARY KEY,
    facebook_user_id VARCHAR(100) NOT NULL,
    page_id VARCHAR(100) NOT NULL,
    user_name VARCHAR(255) NULL,
    user_avatar VARCHAR(500) NULL,
    status VARCHAR(50) DEFAULT 'active',
    assigned_agent_id INTEGER NULL,
    conversation_type VARCHAR(50) DEFAULT 'auto',
    language VARCHAR(10) DEFAULT 'vi',
    metadata JSONB NULL,
    last_message_at BIGINT NULL,
    created_at BIGINT NOT NULL,
    updated_at BIGINT NULL,
    tenant_id INTEGER NOT NULL
);

-- 3. Bảng chat_messages
CREATE TABLE chat_messages (
    id SERIAL PRIMARY KEY,
    conversation_id INTEGER NOT NULL,
    facebook_message_id VARCHAR(100) NULL,
    message_type VARCHAR(50) NOT NULL,
    content TEXT NULL,
    attachments JSONB NULL,
    direction VARCHAR(20) NOT NULL,
    sender_type VARCHAR(20) NOT NULL,
    sender_agent_id INTEGER NULL,
    status VARCHAR(20) DEFAULT 'sent',
    is_ai_generated BOOLEAN DEFAULT false,
    ai_context JSONB NULL,
    ai_confidence DECIMAL(3,2) NULL,
    detected_intent VARCHAR(100) NULL,
    extracted_entities JSONB NULL,
    metadata JSONB NULL,
    created_at BIGINT NOT NULL,
    updated_at BIGINT NULL,
    tenant_id INTEGER NOT NULL
);

-- Tạo các index cho hiệu suất
-- Index cho chat_conversations
CREATE INDEX idx_chat_conversations_facebook_user_page ON chat_conversations(facebook_user_id, page_id);
CREATE INDEX idx_chat_conversations_tenant_status ON chat_conversations(tenant_id, status);

-- Index cho chat_messages
CREATE INDEX idx_chat_messages_conversation_created ON chat_messages(conversation_id, created_at);
CREATE INDEX idx_chat_messages_type_status ON chat_messages(message_type, status);

-- Thêm foreign key constraints (tùy chọn)
-- ALTER TABLE chat_messages ADD CONSTRAINT fk_chat_messages_conversation 
--     FOREIGN KEY (conversation_id) REFERENCES chat_conversations(id) ON DELETE CASCADE;

-- ALTER TABLE chat_conversations ADD CONSTRAINT fk_chat_conversations_page 
--     FOREIGN KEY (page_id) REFERENCES facebook_page_configs(page_id) ON DELETE CASCADE;

-- Thêm comment cho các bảng
COMMENT ON TABLE facebook_page_configs IS 'Cấu hình Facebook Page cho chat integration';
COMMENT ON TABLE chat_conversations IS 'Cuộc hội thoại chat với khách hàng';
COMMENT ON TABLE chat_messages IS 'Tin nhắn trong cuộc hội thoại chat';

-- Comment cho các cột quan trọng
COMMENT ON COLUMN chat_messages.direction IS 'Hướng tin nhắn: incoming (từ user), outgoing (từ bot/agent)';
COMMENT ON COLUMN chat_messages.sender_type IS 'Người gửi: user, bot, agent';
COMMENT ON COLUMN chat_messages.message_type IS 'Loại tin nhắn: text, image, file, audio, video, quick_reply, postback';
COMMENT ON COLUMN chat_conversations.status IS 'Trạng thái: active, closed, waiting, transferred';
COMMENT ON COLUMN chat_conversations.conversation_type IS 'Loại cuộc hội thoại: auto (AI), manual (human), mixed';
